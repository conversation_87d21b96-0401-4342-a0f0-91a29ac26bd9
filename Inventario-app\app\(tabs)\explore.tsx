/**
 * Pantalla de prueba para agregar productos desde la app móvil
 * Incluye el formulario de agregar productos y lista de productos creados
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  SafeAreaView
} from 'react-native';
import TestAddProduct from '../../components/test_AddProduct';
import TestProductList from '../../components/test_ProductList';
import { apiService, Producto } from '../../services/apiService';

export default function TestAddProductsScreen() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [productos, setProductos] = useState<Producto[]>([]);
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  useEffect(() => {
    checkConnectionAndLoadProducts();
  }, []);

  const checkConnectionAndLoadProducts = async () => {
    try {
      setLoading(true);
      
      // Verificar conexión
      const isConnected = await apiService.checkConnection();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
      
      if (isConnected) {
        // Cargar productos
        const productosData = await apiService.getProductos();
        setProductos(Array.isArray(productosData) ? productosData : []);
      }
      
    } catch (error) {
      console.error('Error:', error);
      setConnectionStatus('disconnected');
      Alert.alert(
        'Error de Conexión',
        'No se pudo conectar con la API Django. Verifica que esté ejecutándose.',
        [
          { text: 'Reintentar', onPress: checkConnectionAndLoadProducts },
          { text: 'Cancelar', style: 'cancel' }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleProductAdded = async () => {
    // Recargar la lista de productos después de agregar uno nuevo
    await checkConnectionAndLoadProducts();
    setShowAddForm(false);
  };

  const handleProductSelect = (producto: Producto) => {
    Alert.alert(
      'Producto Seleccionado',
      `Nombre: ${producto.nombre}\nSKU: ${producto.sku}\nPrecio: $${producto.precio}\nCategoría: ${producto.categoria_nombre}`,
      [
        {
          text: 'Editar',
          onPress: () => {
            Alert.alert('Info', 'Función de editar en desarrollo');
          }
        },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => confirmDelete(producto)
        },
        {
          text: 'Cancelar',
          style: 'cancel'
        }
      ]
    );
  };

  const confirmDelete = (producto: Producto) => {
    Alert.alert(
      'Confirmar Eliminación',
      `¿Estás seguro de que quieres eliminar "${producto.nombre}"?`,
      [
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => deleteProduct(producto.id)
        },
        {
          text: 'Cancelar',
          style: 'cancel'
        }
      ]
    );
  };

  const deleteProduct = async (productId: number) => {
    try {
      setLoading(true);
      await apiService.deleteProducto(productId);
      
      Alert.alert('Éxito', 'Producto eliminado correctamente');
      
      // Recargar lista
      await checkConnectionAndLoadProducts();
      
    } catch (error) {
      console.error('Error deleting product:', error);
      Alert.alert('Error', 'No se pudo eliminar el producto');
    } finally {
      setLoading(false);
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#4CAF50';
      case 'disconnected': return '#F44336';
      case 'checking': return '#FF9800';
      default: return '#999';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Conectado a Django API';
      case 'disconnected': return 'Desconectado de la API';
      case 'checking': return 'Verificando conexión...';
      default: return 'Estado desconocido';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Gestión de Productos</Text>
        <View style={[styles.connectionStatus, { backgroundColor: getConnectionStatusColor() }]}>
          <Text style={styles.connectionText}>{getConnectionStatusText()}</Text>
        </View>
      </View>

      {/* Estadísticas */}
      <View style={styles.statsContainer}>
        <View style={styles.statsCard}>
          <Text style={styles.statsNumber}>{productos.length}</Text>
          <Text style={styles.statsLabel}>Total Productos</Text>
        </View>
        <View style={styles.statsCard}>
          <Text style={styles.statsNumber}>
            {productos.filter(p => p.activo).length}
          </Text>
          <Text style={styles.statsLabel}>Activos</Text>
        </View>
        <View style={styles.statsCard}>
          <Text style={styles.statsNumber}>
            {productos.filter(p => !p.activo).length}
          </Text>
          <Text style={styles.statsLabel}>Inactivos</Text>
        </View>
      </View>

      {/* Botón Agregar */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.addButton, connectionStatus !== 'connected' && styles.disabledButton]}
          onPress={() => setShowAddForm(true)}
          disabled={connectionStatus !== 'connected'}
        >
          <Text style={styles.addButtonText}>➕ Agregar Producto</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={checkConnectionAndLoadProducts}
        >
          <Text style={styles.refreshButtonText}>🔄 Actualizar</Text>
        </TouchableOpacity>
      </View>

      {/* Lista de Productos */}
      <View style={styles.listContainer}>
        <Text style={styles.sectionTitle}>Productos Creados</Text>
        <TestProductList onProductSelect={handleProductSelect} />
      </View>

      {/* Modal para Agregar Producto */}
      <Modal
        visible={showAddForm}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <TestAddProduct
          onProductAdded={handleProductAdded}
          onCancel={() => setShowAddForm(false)}
        />
      </Modal>

      {/* Mensaje de desconexión */}
      {connectionStatus === 'disconnected' && (
        <View style={styles.disconnectedBanner}>
          <Text style={styles.disconnectedText}>
            ⚠️ Sin conexión a Django. Verifica que esté ejecutándose en http://localhost:8000
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={checkConnectionAndLoadProducts}
          >
            <Text style={styles.retryButtonText}>Reintentar</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    paddingBottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  connectionStatus: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    alignSelf: 'flex-start',
  },
  connectionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 15,
    gap: 10,
  },
  statsCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statsNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  statsLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingBottom: 15,
    gap: 10,
  },
  addButton: {
    flex: 2,
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  refreshButton: {
    flex: 1,
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  disconnectedBanner: {
    backgroundColor: '#F44336',
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  disconnectedText: {
    color: 'white',
    fontSize: 14,
    flex: 1,
    marginRight: 10,
  },
  retryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
