/**
 * Pantalla de prueba para mostrar el inventario conectado a la API Django
 * Esta pantalla reemplaza temporalmente una de las tabs para probar la conexión
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService, Inventario, Producto } from '../../services/apiService';
import TestProductList from '../../components/test_ProductList';

export default function TestInventoryScreen() {
  const [inventario, setInventario] = useState<Inventario[]>([]);
  const [productos, setProductos] = useState<Producto[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [activeTab, setActiveTab] = useState<'inventory' | 'products'>('inventory');

  useEffect(() => {
    checkConnection();
    loadData();
  }, []);

  const checkConnection = async () => {
    try {
      const isConnected = await apiService.checkConnection();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
    } catch (error) {
      setConnectionStatus('disconnected');
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Cargar inventario y productos en paralelo
      const [inventarioData, productosData] = await Promise.all([
        apiService.getInventario(),
        apiService.getProductos()
      ]);
      
      setInventario(inventarioData);
      setProductos(productosData);
      setConnectionStatus('connected');
      
    } catch (error) {
      console.error('Error loading data:', error);
      setConnectionStatus('disconnected');
      Alert.alert(
        'Error de Conexión',
        'No se pudo conectar con la API. Verifica que Django esté ejecutándose en http://localhost:8000',
        [
          { text: 'Reintentar', onPress: loadData },
          { text: 'Cancelar', style: 'cancel' }
        ]
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#4CAF50';
      case 'disconnected': return '#F44336';
      case 'checking': return '#FF9800';
      default: return '#999';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Conectado a Django API';
      case 'disconnected': return 'Desconectado de la API';
      case 'checking': return 'Verificando conexión...';
      default: return 'Estado desconocido';
    }
  };

  const renderInventoryItem = (item: Inventario) => (
    <View key={item.id} style={styles.inventoryCard}>
      <View style={styles.inventoryHeader}>
        <Text style={styles.productName}>{item.producto_nombre}</Text>
        <Text style={[
          styles.stockStatus,
          { color: item.necesita_restock ? '#F44336' : '#4CAF50' }
        ]}>
          {item.necesita_restock ? 'Bajo Stock' : 'Stock OK'}
        </Text>
      </View>
      
      <View style={styles.inventoryDetails}>
        <Text style={styles.inventoryText}>SKU: {item.producto_sku}</Text>
        <Text style={styles.inventoryText}>Ubicación: {item.ubicacion_nombre}</Text>
        <Text style={styles.inventoryText}>Cantidad: {item.cantidad}</Text>
        <Text style={styles.inventoryText}>Mínimo: {item.minimo}</Text>
      </View>
    </View>
  );

  const renderStatsCard = (title: string, value: number, color: string) => (
    <View style={[styles.statsCard, { borderLeftColor: color }]}>
      <Text style={styles.statsValue}>{value}</Text>
      <Text style={styles.statsTitle}>{title}</Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Conectando con la API...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const bajoStock = Array.isArray(inventario) ? inventario.filter(item => item.necesita_restock).length : 0;
  const totalProductos = Array.isArray(productos) ? productos.length : 0;
  const totalInventario = Array.isArray(inventario) ? inventario.length : 0;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header con estado de conexión */}
      <View style={styles.header}>
        <Text style={styles.title}>Test Inventario API</Text>
        <View style={[styles.connectionStatus, { backgroundColor: getConnectionStatusColor() }]}>
          <Text style={styles.connectionText}>{getConnectionStatusText()}</Text>
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'inventory' && styles.activeTab]}
          onPress={() => setActiveTab('inventory')}
        >
          <Text style={[styles.tabText, activeTab === 'inventory' && styles.activeTabText]}>
            Inventario
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'products' && styles.activeTab]}
          onPress={() => setActiveTab('products')}
        >
          <Text style={[styles.tabText, activeTab === 'products' && styles.activeTabText]}>
            Productos
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'inventory' ? (
        <ScrollView
          style={styles.content}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
          {/* Estadísticas */}
          <View style={styles.statsContainer}>
            {renderStatsCard('Total Productos', totalProductos, '#2196F3')}
            {renderStatsCard('Items Inventario', totalInventario, '#4CAF50')}
            {renderStatsCard('Bajo Stock', bajoStock, '#F44336')}
          </View>

          {/* Lista de inventario */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Inventario Actual</Text>
            {!Array.isArray(inventario) || inventario.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No hay items en el inventario</Text>
                <Text style={styles.emptySubtext}>
                  Agrega productos y ubicaciones desde la interfaz web
                </Text>
              </View>
            ) : (
              inventario.map(renderInventoryItem)
            )}
          </View>
        </ScrollView>
      ) : (
        <TestProductList
          onProductSelect={(producto) => {
            Alert.alert(
              'Producto Seleccionado',
              `${producto.nombre}\nSKU: ${producto.sku}\nPrecio: $${producto.precio}`,
              [{ text: 'OK' }]
            );
          }}
        />
      )}

      {/* Botón de reconexión */}
      {connectionStatus === 'disconnected' && (
        <View style={styles.reconnectContainer}>
          <TouchableOpacity style={styles.reconnectButton} onPress={loadData}>
            <Text style={styles.reconnectButtonText}>Reconectar</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    paddingBottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  connectionStatus: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    alignSelf: 'flex-start',
  },
  connectionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#2196F3',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 15,
    gap: 10,
  },
  statsCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statsTitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  section: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  inventoryCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  inventoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  stockStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  inventoryDetails: {
    gap: 2,
  },
  inventoryText: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 5,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  reconnectContainer: {
    padding: 15,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  reconnectButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  reconnectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
