/**
 * Componente de prueba para agregar productos desde la app móvil
 * Permite crear productos nuevos conectándose directamente a la API Django
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { apiService, Categoria, Proveedor } from '../services/apiService';

interface TestAddProductProps {
  onProductAdded?: () => void;
  onCancel?: () => void;
}

export default function TestAddProduct({ onProductAdded, onCancel }: TestAddProductProps) {
  // Estados del formulario
  const [nombre, setNombre] = useState('');
  const [sku, setSku] = useState('');
  const [codigoBarras, setCodigoBarras] = useState('');
  const [precio, setPrecio] = useState('');
  const [categoriaId, setCategoriaId] = useState<number | null>(null);
  const [proveedorId, setProveedorId] = useState<number | null>(null);

  // Estados de datos
  const [categorias, setCategorias] = useState<Categoria[]>([]);
  const [proveedores, setProveedores] = useState<Proveedor[]>([]);
  
  // Estados de UI
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);

  // Cargar categorías y proveedores al montar
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoadingData(true);
      const [categoriasData, proveedoresData] = await Promise.all([
        apiService.getCategorias(),
        apiService.getProveedores()
      ]);
      
      setCategorias(Array.isArray(categoriasData) ? categoriasData : []);
      setProveedores(Array.isArray(proveedoresData) ? proveedoresData : []);
      
      // Seleccionar primera categoría por defecto
      if (categoriasData.length > 0) {
        setCategoriaId(categoriasData[0].id);
      }
      if (proveedoresData.length > 0) {
        setProveedorId(proveedoresData[0].id);
      }
      
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'No se pudieron cargar las categorías y proveedores');
    } finally {
      setLoadingData(false);
    }
  };

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6);
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    return `TEST-${timestamp}-${randomNum}`;
  };

  const generateBarcode = () => {
    return Math.floor(Math.random() * 9000000000000) + 1000000000000;
  };

  const autoFillTestData = () => {
    const testNames = [
      'Laptop Test', 'Mouse Test', 'Teclado Test', 'Monitor Test', 
      'Auriculares Test', 'Webcam Test', 'Tablet Test', 'Smartphone Test'
    ];
    const randomName = testNames[Math.floor(Math.random() * testNames.length)];
    
    setNombre(randomName);
    setSku(generateSKU());
    setCodigoBarras(generateBarcode().toString());
    setPrecio((Math.random() * 1000 + 50).toFixed(2));
  };

  const validateForm = () => {
    if (!nombre.trim()) {
      Alert.alert('Error', 'El nombre del producto es requerido');
      return false;
    }
    if (!sku.trim()) {
      Alert.alert('Error', 'El SKU es requerido');
      return false;
    }
    if (!codigoBarras.trim()) {
      Alert.alert('Error', 'El código de barras es requerido');
      return false;
    }
    if (!precio.trim() || isNaN(parseFloat(precio))) {
      Alert.alert('Error', 'El precio debe ser un número válido');
      return false;
    }
    if (!categoriaId) {
      Alert.alert('Error', 'Debe seleccionar una categoría');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      
      const nuevoProducto = {
        nombre: nombre.trim(),
        sku: sku.trim(),
        codigo_barras: codigoBarras.trim(),
        precio: parseFloat(precio),
        categoria: categoriaId!,
        proveedor: proveedorId,
        activo: true
      };

      const productoCreado = await apiService.createProducto(nuevoProducto);
      
      Alert.alert(
        '¡Éxito!',
        `Producto "${productoCreado.nombre}" creado correctamente\nSKU: ${productoCreado.sku}`,
        [
          {
            text: 'OK',
            onPress: () => {
              // Limpiar formulario
              setNombre('');
              setSku('');
              setCodigoBarras('');
              setPrecio('');
              
              // Notificar que se agregó un producto
              onProductAdded?.();
            }
          }
        ]
      );
      
    } catch (error) {
      console.error('Error creating product:', error);
      Alert.alert(
        'Error',
        'No se pudo crear el producto. Verifica que el SKU y código de barras sean únicos.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Cargando formulario...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Agregar Producto</Text>
          <Text style={styles.subtitle}>Conectado a Django API</Text>
        </View>

        <View style={styles.form}>
          {/* Botón de datos de prueba */}
          <TouchableOpacity style={styles.testDataButton} onPress={autoFillTestData}>
            <Text style={styles.testDataButtonText}>📝 Llenar con Datos de Prueba</Text>
          </TouchableOpacity>

          {/* Nombre */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nombre del Producto *</Text>
            <TextInput
              style={styles.input}
              value={nombre}
              onChangeText={setNombre}
              placeholder="Ej: Laptop HP Pavilion"
              placeholderTextColor="#999"
            />
          </View>

          {/* SKU */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>SKU *</Text>
            <View style={styles.inputWithButton}>
              <TextInput
                style={[styles.input, styles.inputFlex]}
                value={sku}
                onChangeText={setSku}
                placeholder="Ej: HP-001"
                placeholderTextColor="#999"
              />
              <TouchableOpacity 
                style={styles.generateButton} 
                onPress={() => setSku(generateSKU())}
              >
                <Text style={styles.generateButtonText}>🎲</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Código de Barras */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Código de Barras *</Text>
            <View style={styles.inputWithButton}>
              <TextInput
                style={[styles.input, styles.inputFlex]}
                value={codigoBarras}
                onChangeText={setCodigoBarras}
                placeholder="1234567890123"
                placeholderTextColor="#999"
                keyboardType="numeric"
              />
              <TouchableOpacity 
                style={styles.generateButton} 
                onPress={() => setCodigoBarras(generateBarcode().toString())}
              >
                <Text style={styles.generateButtonText}>🎲</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Precio */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Precio *</Text>
            <TextInput
              style={styles.input}
              value={precio}
              onChangeText={setPrecio}
              placeholder="99.99"
              placeholderTextColor="#999"
              keyboardType="decimal-pad"
            />
          </View>

          {/* Categoría */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Categoría *</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={categoriaId}
                onValueChange={setCategoriaId}
                style={styles.picker}
              >
                {categorias.map((categoria) => (
                  <Picker.Item 
                    key={categoria.id} 
                    label={categoria.nombre} 
                    value={categoria.id} 
                  />
                ))}
              </Picker>
            </View>
          </View>

          {/* Proveedor */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Proveedor</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={proveedorId}
                onValueChange={setProveedorId}
                style={styles.picker}
              >
                <Picker.Item label="Sin proveedor" value={null} />
                {proveedores.map((proveedor) => (
                  <Picker.Item 
                    key={proveedor.id} 
                    label={proveedor.nombre} 
                    value={proveedor.id} 
                  />
                ))}
              </Picker>
            </View>
          </View>
        </View>

        {/* Botones */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.button, styles.cancelButton]} 
            onPress={onCancel}
          >
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.submitButton, loading && styles.disabledButton]} 
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>Crear Producto</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  form: {
    padding: 20,
  },
  testDataButton: {
    backgroundColor: '#FF9800',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  testDataButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  inputWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputFlex: {
    flex: 1,
    marginRight: 10,
  },
  generateButton: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 8,
    minWidth: 50,
    alignItems: 'center',
  },
  generateButtonText: {
    fontSize: 18,
  },
  pickerContainer: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
  },
  picker: {
    height: 50,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 15,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f44336',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#4CAF50',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
});
