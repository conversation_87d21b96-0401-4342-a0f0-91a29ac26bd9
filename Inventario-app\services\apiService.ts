import Constants from 'expo-constants';

// Configuración de la API
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8000/api';
const API_TIMEOUT = parseInt(process.env.EXPO_PUBLIC_API_TIMEOUT || '10000');

// Tipos de datos
export interface Categoria {
  id: number;
  nombre: string;
}

export interface Proveedor {
  id: number;
  nombre: string;
}

export interface Producto {
  id: number;
  nombre: string;
  sku: string;
  codigo_barras: string;
  precio: string;
  categoria: number;
  categoria_nombre: string;
  proveedor: number;
  proveedor_nombre: string;
  imagen_principal: string | null;
  activo: boolean;
  imagenes?: ImagenProducto[];
}

export interface ImagenProducto {
  id: number;
  producto: number;
  url: string | null;
  imagen_base64: string | null;
  tipo_imagen: 'URL' | 'BASE64';
  es_principal: boolean;
  fecha_creacion: string;
  imagen_data: string;
}

export interface Ubicacion {
  id: number;
  nombre: string;
}

export interface Inventario {
  id: number;
  producto: number;
  producto_nombre: string;
  producto_sku: string;
  ubicacion: number;
  ubicacion_nombre: string;
  cantidad: number;
  minimo: number;
  necesita_restock: boolean;
}

export interface Usuario {
  id: number;
  nombre_usuario: string;
  rol: number;
  rol_nombre: string;
  activo: boolean;
}

export interface MovimientoInventario {
  id: number;
  producto: number;
  producto_nombre: string;
  ubicacion: number;
  ubicacion_nombre: string;
  tipo: 'ENTRADA' | 'SALIDA' | 'AJUSTE';
  cantidad: number;
  usuario: number;
  usuario_nombre: string;
  imagen: string | null;
  fecha: string;
}

// Clase principal del servicio API
class ApiService {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.timeout = API_TIMEOUT;
  }

  // Método genérico para hacer requests
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json() as T;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Métodos GET
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // Métodos POST
  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Métodos PUT
  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Métodos DELETE
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // === CATEGORÍAS ===
  async getCategorias(): Promise<Categoria[]> {
    return this.get<Categoria[]>('/categorias/');
  }

  async createCategoria(categoria: Omit<Categoria, 'id'>): Promise<Categoria> {
    return this.post<Categoria>('/categorias/', categoria);
  }

  // === PROVEEDORES ===
  async getProveedores(): Promise<Proveedor[]> {
    return this.get<Proveedor[]>('/proveedores/');
  }

  async createProveedor(proveedor: Omit<Proveedor, 'id'>): Promise<Proveedor> {
    return this.post<Proveedor>('/proveedores/', proveedor);
  }

  // === PRODUCTOS ===
  async getProductos(): Promise<Producto[]> {
    return this.get<Producto[]>('/productos/');
  }

  async getProducto(id: number): Promise<Producto> {
    return this.get<Producto>(`/productos/${id}/`);
  }

  async createProducto(producto: Omit<Producto, 'id' | 'categoria_nombre' | 'proveedor_nombre' | 'imagenes'>): Promise<Producto> {
    return this.post<Producto>('/productos/', producto);
  }

  async updateProducto(id: number, producto: Partial<Producto>): Promise<Producto> {
    return this.put<Producto>(`/productos/${id}/`, producto);
  }

  async deleteProducto(id: number): Promise<void> {
    return this.delete<void>(`/productos/${id}/`);
  }

  // === IMÁGENES ===
  async subirFotoBase64(data: {
    producto: number;
    imagen_base64: string;
    es_principal: boolean;
  }): Promise<ImagenProducto> {
    return this.post<ImagenProducto>('/imagenes-productos/subir_foto/', data);
  }

  async agregarImagenURL(data: {
    producto: number;
    url: string;
    es_principal: boolean;
  }): Promise<ImagenProducto> {
    return this.post<ImagenProducto>('/imagenes-productos/agregar_url/', data);
  }

  // === UBICACIONES ===
  async getUbicaciones(): Promise<Ubicacion[]> {
    return this.get<Ubicacion[]>('/ubicaciones/');
  }

  async createUbicacion(ubicacion: Omit<Ubicacion, 'id'>): Promise<Ubicacion> {
    return this.post<Ubicacion>('/ubicaciones/', ubicacion);
  }

  // === INVENTARIO ===
  async getInventario(): Promise<Inventario[]> {
    return this.get<Inventario[]>('/inventario/');
  }

  async getInventarioBajoStock(): Promise<Inventario[]> {
    return this.get<Inventario[]>('/inventario/bajo_stock/');
  }

  async updateInventario(id: number, inventario: Partial<Inventario>): Promise<Inventario> {
    return this.put<Inventario>(`/inventario/${id}/`, inventario);
  }

  // === MOVIMIENTOS ===
  async getMovimientos(): Promise<MovimientoInventario[]> {
    return this.get<MovimientoInventario[]>('/movimientos/');
  }

  async createMovimiento(movimiento: {
    producto: number;
    ubicacion: number;
    tipo: 'ENTRADA' | 'SALIDA' | 'AJUSTE';
    cantidad: number;
    usuario: number;
    imagen?: string;
  }): Promise<MovimientoInventario> {
    return this.post<MovimientoInventario>('/movimientos/', movimiento);
  }

  // === USUARIOS ===
  async getUsuarios(): Promise<Usuario[]> {
    return this.get<Usuario[]>('/usuarios/');
  }

  // Método para verificar conectividad
  async checkConnection(): Promise<boolean> {
    try {
      await this.get('/categorias/');
      return true;
    } catch (error) {
      console.error('Connection check failed:', error);
      return false;
    }
  }
}

// Exportar instancia singleton
export const apiService = new ApiService();
export default apiService;
