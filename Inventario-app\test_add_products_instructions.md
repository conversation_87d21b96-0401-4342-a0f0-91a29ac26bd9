# 📱 Módulo de Agregar Productos - Instrucciones

## 🎯 ¿Qué hace este módulo?

El módulo **test_add_products** permite:
- ✅ **Agregar productos** desde la app móvil directamente a Django
- ✅ **Ver lista de productos** en tiempo real
- ✅ **Eliminar productos** con confirmación
- ✅ **Generar datos automáticamente** para pruebas
- ✅ **Validar formularios** antes de enviar
- ✅ **Mostrar estadísticas** de productos

## 🚀 Cómo Activar el Módulo

### Opción 1: Reemplazar Tab Temporal
```bash
# Renombrar archivo existente
mv app/(tabs)/explore.tsx app/(tabs)/explore_backup.tsx

# Renombrar módulo de agregar productos
mv app/(tabs)/test_add_products.tsx app/(tabs)/explore.tsx
```

### Opción 2: Agregar Nueva Tab
Edita `app/(tabs)/_layout.tsx` y agrega:
```typescript
<Tabs.Screen
  name="test_add_products"
  options={{
    title: 'Agregar',
    tabBarIcon: ({ color }) => <IconSymbol size={28} name="plus.circle.fill" color={color} />,
  }}
/>
```

## 📋 Funcionalidades del Módulo

### 🟢 Pantalla Principal
- **Estadísticas**: Total productos, activos, inactivos
- **Estado de conexión**: Verde (conectado) / Rojo (desconectado)
- **Botón "Agregar Producto"**: Abre formulario modal
- **Botón "Actualizar"**: Recarga datos desde Django
- **Lista de productos**: Muestra todos los productos

### ➕ Formulario de Agregar
- **Campos requeridos**: Nombre, SKU, Código de barras, Precio, Categoría
- **Campo opcional**: Proveedor
- **Botón "Datos de prueba"**: Llena automáticamente con datos aleatorios
- **Botones 🎲**: Generan SKU y código de barras únicos
- **Validaciones**: Verifica que todos los campos estén correctos

### 🛍️ Lista de Productos
- **Tap en producto**: Muestra opciones (Editar/Eliminar)
- **Información completa**: Nombre, SKU, precio, categoría, proveedor
- **Imágenes**: Si el producto tiene imagen principal
- **Estado**: Activo/Inactivo

## 🔧 Cómo Usar

### 1. Asegúrate que Django esté ejecutándose
```bash
cd inventario-app-web
python manage.py runserver
```

### 2. Asegúrate que Expo esté ejecutándose
```bash
cd inventario-app-movil/Inventario-app
npx expo start
```

### 3. Activar el módulo (Opción 1 - Más fácil)
```bash
# En la carpeta inventario-app-movil/Inventario-app
mv app/(tabs)/explore.tsx app/(tabs)/explore_backup.tsx
mv app/(tabs)/test_add_products.tsx app/(tabs)/explore.tsx
```

### 4. Abrir la app
- **Web**: Presiona `w` en terminal de Expo
- **Móvil**: Escanea QR con Expo Go
- Ve a la tab **"Explore"** (ahora es el módulo de agregar productos)

## 📱 Flujo de Uso

### Agregar Producto Nuevo:
1. **Tap "➕ Agregar Producto"**
2. **Tap "📝 Llenar con Datos de Prueba"** (opcional)
3. **Editar campos** según necesites
4. **Usar botones 🎲** para generar SKU/código únicos
5. **Seleccionar categoría** (requerido)
6. **Seleccionar proveedor** (opcional)
7. **Tap "Crear Producto"**
8. **¡Listo!** El producto se crea en Django

### Ver/Gestionar Productos:
1. **Lista automática** se actualiza al agregar
2. **Tap en cualquier producto** para ver opciones
3. **Eliminar**: Confirma y elimina de Django
4. **Editar**: (En desarrollo)

## 🧪 Datos de Prueba

### El botón "Datos de Prueba" genera:
- **Nombres**: "Laptop Test", "Mouse Test", "Teclado Test", etc.
- **SKU**: "TEST-123456-78" (único)
- **Código de barras**: 13 dígitos aleatorios
- **Precio**: Entre $50 y $1050 (aleatorio)

### Los botones 🎲 generan:
- **SKU**: Formato "TEST-[timestamp]-[random]"
- **Código de barras**: 13 dígitos únicos

## ⚠️ Validaciones

### El formulario valida:
- ✅ **Nombre**: No puede estar vacío
- ✅ **SKU**: Debe ser único en Django
- ✅ **Código de barras**: Debe ser único en Django
- ✅ **Precio**: Debe ser un número válido
- ✅ **Categoría**: Debe seleccionar una

### Si hay errores:
- 🔴 **SKU duplicado**: "Verifica que el SKU sea único"
- 🔴 **Código duplicado**: "Verifica que el código de barras sea único"
- 🔴 **Campos vacíos**: Mensaje específico por campo

## 🔍 Solución de Problemas

### "No se pudo conectar con la API"
- ✅ Verifica que Django esté en `http://localhost:8000`
- ✅ Ejecuta: `python manage.py runserver`
- ✅ Verifica CORS en Django

### "No se pudieron cargar categorías"
- ✅ Crea categorías desde Django admin
- ✅ O ejecuta: `python test_django_api.py`

### "SKU ya existe"
- ✅ Usa el botón 🎲 para generar SKU único
- ✅ O cambia manualmente el SKU

### App no carga el módulo
- ✅ Verifica que renombraste los archivos correctamente
- ✅ Reinicia Expo: Ctrl+C y `npx expo start`

## 📊 Endpoints Utilizados

### El módulo usa estos endpoints de Django:
- `GET /api/categorias/` - Cargar categorías
- `GET /api/proveedores/` - Cargar proveedores
- `GET /api/productos/` - Cargar productos
- `POST /api/productos/` - Crear producto
- `DELETE /api/productos/{id}/` - Eliminar producto

## 🎯 Próximas Funcionalidades

### En desarrollo:
- [ ] **Editar productos** existentes
- [ ] **Subir imágenes** desde la cámara
- [ ] **Escanear códigos de barras**
- [ ] **Búsqueda y filtros**
- [ ] **Gestión de inventario** desde la app

## 🔄 Volver al Estado Original

### Para restaurar la app original:
```bash
# Restaurar archivo original
mv app/(tabs)/explore.tsx app/(tabs)/test_add_products.tsx
mv app/(tabs)/explore_backup.tsx app/(tabs)/explore.tsx
```

## 📞 Comandos Rápidos

```bash
# Verificar Django
curl http://localhost:8000/api/productos/

# Verificar conexión desde app
npx ts-node --project tsconfig.node.json test_api_connection.ts

# Reiniciar Expo
# Ctrl+C en terminal de Expo, luego:
npx expo start
```
