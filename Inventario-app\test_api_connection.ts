/**
 * Archivo de prueba para verificar la conexión con la API Django
 * Ejecutar: npx ts-node test_api_connection.ts
 */

import { apiService } from './services/apiService';

async function testApiConnection() {
  console.log('🔄 Iniciando pruebas de conexión con la API...\n');

  try {
    // 1. Verificar conectividad básica
    console.log('1. Verificando conectividad básica...');
    const isConnected = await apiService.checkConnection();
    console.log(`   ✅ Conexión: ${isConnected ? 'EXITOSA' : 'FALLIDA'}\n`);

    if (!isConnected) {
      console.log('❌ No se pudo conectar con la API. Verifica que Django esté ejecutándose en http://localhost:8000');
      return;
    }

    // 2. Probar endpoints principales
    console.log('2. Probando endpoints principales...');

    // Categorías
    try {
      const categorias = await apiService.getCategorias();
      console.log(`   ✅ Categorías: ${categorias.length} encontradas`);
      if (categorias.length > 0) {
        console.log(`      - Ejemplo: ${categorias[0].nombre}`);
      }
    } catch (error) {
      console.log(`   ❌ Error en categorías: ${error}`);
    }

    // Proveedores
    try {
      const proveedores = await apiService.getProveedores();
      console.log(`   ✅ Proveedores: ${proveedores.length} encontrados`);
      if (proveedores.length > 0) {
        console.log(`      - Ejemplo: ${proveedores[0].nombre}`);
      }
    } catch (error) {
      console.log(`   ❌ Error en proveedores: ${error}`);
    }

    // Productos
    try {
      const productos = await apiService.getProductos();
      console.log(`   ✅ Productos: ${productos.length} encontrados`);
      if (productos.length > 0) {
        console.log(`      - Ejemplo: ${productos[0].nombre} (SKU: ${productos[0].sku})`);
      }
    } catch (error) {
      console.log(`   ❌ Error en productos: ${error}`);
    }

    // Ubicaciones
    try {
      const ubicaciones = await apiService.getUbicaciones();
      console.log(`   ✅ Ubicaciones: ${ubicaciones.length} encontradas`);
      if (ubicaciones.length > 0) {
        console.log(`      - Ejemplo: ${ubicaciones[0].nombre}`);
      }
    } catch (error) {
      console.log(`   ❌ Error en ubicaciones: ${error}`);
    }

    // Inventario
    try {
      const inventario = await apiService.getInventario();
      console.log(`   ✅ Inventario: ${inventario.length} registros encontrados`);
      if (inventario.length > 0) {
        console.log(`      - Ejemplo: ${inventario[0].producto_nombre} - Cantidad: ${inventario[0].cantidad}`);
      }
    } catch (error) {
      console.log(`   ❌ Error en inventario: ${error}`);
    }

    // Movimientos
    try {
      const movimientos = await apiService.getMovimientos();
      console.log(`   ✅ Movimientos: ${movimientos.length} encontrados`);
      if (movimientos.length > 0) {
        console.log(`      - Ejemplo: ${movimientos[0].tipo} - ${movimientos[0].producto_nombre}`);
      }
    } catch (error) {
      console.log(`   ❌ Error en movimientos: ${error}`);
    }

    console.log('\n🎉 Pruebas de conexión completadas exitosamente!');
    console.log('\n📱 La app móvil puede conectarse correctamente con la API Django.');

  } catch (error) {
    console.error('\n❌ Error general en las pruebas:', error);
    console.log('\n🔧 Posibles soluciones:');
    console.log('   1. Verifica que Django esté ejecutándose: python manage.py runserver');
    console.log('   2. Verifica la URL de la API en el archivo .env');
    console.log('   3. Verifica que CORS esté configurado correctamente en Django');
    console.log('   4. Si usas un dispositivo físico, cambia localhost por tu IP local');
  }
}

// Función para probar creación de datos
async function testDataCreation() {
  console.log('\n🔄 Probando creación de datos de prueba...\n');

  try {
    // Crear categoría de prueba
    console.log('1. Creando categoría de prueba...');
    const nuevaCategoria = await apiService.createCategoria({
      nombre: 'Categoría Test Mobile'
    });
    console.log(`   ✅ Categoría creada: ${nuevaCategoria.nombre} (ID: ${nuevaCategoria.id})`);

    // Crear proveedor de prueba
    console.log('2. Creando proveedor de prueba...');
    const nuevoProveedor = await apiService.createProveedor({
      nombre: 'Proveedor Test Mobile'
    });
    console.log(`   ✅ Proveedor creado: ${nuevoProveedor.nombre} (ID: ${nuevoProveedor.id})`);

    // Crear ubicación de prueba
    console.log('3. Creando ubicación de prueba...');
    const nuevaUbicacion = await apiService.createUbicacion({
      nombre: 'Almacén Test Mobile'
    });
    console.log(`   ✅ Ubicación creada: ${nuevaUbicacion.nombre} (ID: ${nuevaUbicacion.id})`);

    console.log('\n🎉 Datos de prueba creados exitosamente!');

  } catch (error) {
    console.error('\n❌ Error al crear datos de prueba:', error);
  }
}

// Ejecutar pruebas
async function runAllTests() {
  await testApiConnection();
  
  // Preguntar si quiere crear datos de prueba
  console.log('\n❓ ¿Quieres ejecutar también las pruebas de creación de datos? (Esto creará datos de prueba en la base de datos)');
  console.log('   Descomenta la siguiente línea para ejecutar:');
  console.log('   // await testDataCreation();');
  
  // await testDataCreation(); // Descomenta esta línea para probar creación de datos
}

// Ejecutar si se llama directamente
if (require.main === module) {
  runAllTests().catch(console.error);
}

export { testApiConnection, testDataCreation };
