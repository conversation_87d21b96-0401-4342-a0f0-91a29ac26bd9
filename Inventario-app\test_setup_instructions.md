# 📱 Instrucciones de Configuración - App Móvil Inventario

## 🚀 Configuración Inicial

### 1. Instalar Dependencias
```bash
cd inventario-app-movil/Inventario-app
npm install
```

### 2. Configurar Variables de Entorno
El archivo `.env` ya está creado con la configuración básica:
```
EXPO_PUBLIC_API_BASE_URL=http://localhost:8000
EXPO_PUBLIC_API_URL=http://localhost:8000/api
```

**⚠️ IMPORTANTE:** Si vas a probar en un dispositivo físico, cambia `localhost` por tu IP local:
```
EXPO_PUBLIC_API_URL=http://*************:8000/api
```

### 3. Instalar Dependencias Adicionales para TypeScript
```bash
npm install --save-dev ts-node typescript @types/node
```

## 🧪 Ejecutar Pruebas

### Prueba 1: Verificar Conexión API
```bash
npx ts-node test_api_connection.ts
```

### Prueba 2: Ejecutar App con Pantalla de Prueba
```bash
npx expo start
```

## 📱 Usar la Pantalla de Prueba

1. **Modificar el layout de tabs** para incluir la pantalla de prueba:
   - Edita `app/(tabs)/_layout.tsx`
   - Agrega una nueva tab que apunte a `test_inventory`

2. **O reemplazar temporalmente una tab existente:**
   - Renombra `app/(tabs)/explore.tsx` a `app/(tabs)/explore_backup.tsx`
   - Renombra `app/(tabs)/test_inventory.tsx` a `app/(tabs)/explore.tsx`

## 🔧 Solución de Problemas

### Error: "Network request failed"
- Verifica que Django esté ejecutándose en `http://localhost:8000`
- Si usas dispositivo físico, cambia `localhost` por tu IP local en `.env`
- Verifica que CORS esté configurado en Django

### Error: "Unable to resolve module"
```bash
npm install
npx expo install --fix
```

### Error de TypeScript
```bash
npm install --save-dev @types/react @types/react-native
```

## 📊 Funcionalidades de la Pantalla de Prueba

### Tab "Inventario"
- ✅ Muestra estadísticas de conexión
- ✅ Lista items del inventario
- ✅ Indica productos con bajo stock
- ✅ Refresh para recargar datos

### Tab "Productos"
- ✅ Lista todos los productos
- ✅ Muestra imágenes si están disponibles
- ✅ Información completa (SKU, categoría, proveedor)
- ✅ Estado activo/inactivo

### Indicadores de Estado
- 🟢 **Verde**: Conectado a la API
- 🔴 **Rojo**: Desconectado de la API
- 🟡 **Amarillo**: Verificando conexión

## 🌐 URLs de Prueba

### Desarrollo Local
- **API Base**: `http://localhost:8000/api`
- **Admin Django**: `http://localhost:8000/admin`
- **Expo Web**: `http://localhost:19006`

### Red Local (Dispositivo Físico)
- **API Base**: `http://[TU_IP]:8000/api`
- Ejemplo: `http://*************:8000/api`

## 📝 Próximos Pasos

1. **Probar conexión básica** con `test_api_connection.ts`
2. **Ejecutar app móvil** y verificar pantalla de prueba
3. **Crear datos de prueba** desde Django admin o API
4. **Implementar funcionalidades específicas** según necesidades
5. **Agregar autenticación** si es necesario
6. **Optimizar para producción** (cambiar URLs, configurar build)

## 🔐 Seguridad

### Para Desarrollo
- CORS configurado para permitir todas las conexiones
- Sin autenticación requerida
- Debug habilitado

### Para Producción
- [ ] Configurar CORS específico
- [ ] Implementar autenticación JWT
- [ ] Usar HTTPS
- [ ] Variables de entorno seguras
- [ ] Deshabilitar debug

## 📞 Soporte

Si encuentras problemas:
1. Verifica que Django esté ejecutándose
2. Revisa los logs de la consola
3. Verifica la configuración de red
4. Consulta la documentación de Expo
